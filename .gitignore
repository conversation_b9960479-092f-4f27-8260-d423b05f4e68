# 大型数据文件
*.mat
*.csv
*.xls
*.xlsx

# 音频文件
*.wav
*.mp3
*.m4a
*.aac
*.flac

# 视频文件
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv

# 图像文件
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff
*.svg

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.gz

# MATLAB特定文件
*.asv
*.slxc
*.p
*.mex*
*.fig

# R相关文件
.RData
.Rhistory
Rplot*.svg
Rplot*.png
Rplot*.pdf

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 临时文件
*.tmp
*.temp
*~
*.bak
*.backup

# 日志文件
*.log

# 大型目录（根据您的文件结构）
**/1、Raw data/
**/2、Processed data/
**/3、Backup/
**/video/
**/audio/

# 特定的大型文件夹
matlab_workflow/1、Signal_process/1. Read files (audio segments, video segments)/
matlab_workflow/1、Signal_process/2. Signal denoising/2、Processed data/
matlab_workflow/1、Signal_process/3. Signal labeling/1、信号标注器APP标注/1、Raw data/
matlab_workflow/1、Signal_process/3. Signal labeling/2、标注得到数据集/1、Raw data/
matlab_workflow/1、Signal_process/3. Signal labeling/4、整理数据集/1、Raw data/
matlab_workflow/1、Signal_process/5. Signal plotting/1、数据准备/2、Processed data/
matlab_workflow/1、Signal_process/5. Signal plotting/2、肠鸣音可视化/1、Raw data/

# FigureBest工具包（如果不需要版本控制）
matlab_workflow/1、Signal_process/5. Signal plotting/5、FigureBest/FigureBest-4.7.1.3/

# 其他大型工具包和数据目录
**/FigureBest*/
**/BAMS_System*/
**/MATLABcode/speech_signal/
**/MATLABcode/*/

# 排除所有包含"Raw data"、"Processed data"、"Backup"的目录
**/*Raw data*/
**/*Processed data*/
**/*Backup*/

# 排除特定的大型文件夹
matlab_workflow/4、借鉴/
